import {
  publicProcedure,
  router,
} from "../lib/trpc";
import { supabase, createServerSupabaseClient, type ContentPiece, type SearchResult } from "../lib/supabase";
import { z } from "zod";

export const appRouter = router({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),

  // Get Twitter Spaces (sorted by tune-in numbers)
  getTwitterSpaces: publicProcedure.query(async () => {
    const { data, error } = await supabase
      .from('content_pieces')
      .select('*')
      .eq('twitter_content_type', 'space')
      .order('twitter_impressions', { ascending: false })
      .limit(10);

    if (error) {
      throw new Error(`Failed to fetch Twitter Spaces: ${error.message}`);
    }

    return data as ContentPiece[];
  }),

  // Get Marketing Partners (sponsored content)
  getMarketingPartners: publicProcedure.query(async () => {
    const { data, error } = await supabase
      .from('content_pieces')
      .select('*')
      .contains('content_types', ['marketing'])
      .order('twitter_impressions', { ascending: false })
      .limit(10);

    if (error) {
      throw new Error(`Failed to fetch marketing partners: ${error.message}`);
    }

    return data as ContentPiece[];
  }),

  // Get Testimonials (high-performance content)
  getTestimonials: publicProcedure.query(async () => {
    const { data, error } = await supabase
      .from('content_pieces')
      .select('*')
      .gte('twitter_impressions', 1000000) // 1M+ impressions
      .order('twitter_impressions', { ascending: false })
      .limit(10);

    if (error) {
      throw new Error(`Failed to fetch testimonials: ${error.message}`);
    }

    return data as ContentPiece[];
  }),

  // Search all content
  searchContent: publicProcedure
    .input(z.object({
      query: z.string(),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .rpc('search_content_pieces', {
          search_query: input.query,
          limit_count: input.limit
        });

      if (error) {
        throw new Error(`Search failed: ${error.message}`);
      }

      return data as SearchResult[];
    }),

  // Get all content with pagination
  getAllContent: publicProcedure
    .input(z.object({
      page: z.number().default(0),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .select('*')
        .order('content_created_date', { ascending: false })
        .range(input.page * input.limit, (input.page + 1) * input.limit - 1);

      if (error) {
        throw new Error(`Failed to fetch content: ${error.message}`);
      }

      return data as ContentPiece[];
    }),

  // Get Twitter Tweets (sorted by impressions)
  getTwitterTweets: publicProcedure.query(async () => {
    const { data, error } = await supabase
      .from('content_pieces')
      .select('*')
      .eq('twitter_content_type', 'tweet')
      .order('twitter_impressions', { ascending: false })
      .limit(20);

    if (error) {
      throw new Error(`Failed to fetch Twitter tweets: ${error.message}`);
    }

    return data as ContentPiece[];
  }),

  // Get all Twitter content (Spaces + Tweets)
  getAllTwitterContent: publicProcedure.query(async () => {
    const { data, error } = await supabase
      .from('content_pieces')
      .select('*')
      .contains('content_types', ['twitter'])
      .order('twitter_impressions', { ascending: false })
      .limit(30);

    if (error) {
      throw new Error(`Failed to fetch Twitter content: ${error.message}`);
    }

    return data as ContentPiece[];
  }),

  // Get content by category
  getContentByCategory: publicProcedure
    .input(z.object({
      category: z.string(),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .select('*')
        .contains('content_categories', [input.category])
        .order('twitter_impressions', { ascending: false })
        .limit(input.limit);

      if (error) {
        throw new Error(`Failed to fetch content by category: ${error.message}`);
      }

      return data as ContentPiece[];
    }),

  // Get content by type (twitter, marketing, etc.)
  getContentByType: publicProcedure
    .input(z.object({
      type: z.string(),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .select('*')
        .contains('content_types', [input.type])
        .order('twitter_impressions', { ascending: false })
        .limit(input.limit);

      if (error) {
        throw new Error(`Failed to fetch content by type: ${error.message}`);
      }

      return data as ContentPiece[];
    }),

  // Advanced filtered content with sorting
  getFilteredContent: publicProcedure
    .input(z.object({
      search: z.string().default(""),
      contentType: z.enum(["all", "spaces", "tweets", "marketing"]).default("all"),
      category: z.string().default("all"),
      sortBy: z.enum(["impressions", "date", "likes", "retweets"]).default("impressions"),
      sortOrder: z.enum(["desc", "asc"]).default("desc"),
      limit: z.number().default(30)
    }))
    .query(async ({ input }) => {
      let query = supabase.from('content_pieces').select('*');

      // Apply content type filter
      if (input.contentType !== "all") {
        switch (input.contentType) {
          case "spaces":
            query = query.eq('twitter_content_type', 'space');
            break;
          case "tweets":
            query = query.eq('twitter_content_type', 'tweet');
            break;
          case "marketing":
            query = query.contains('content_types', ['marketing']);
            break;
        }
      }

      // Apply category filter
      if (input.category !== "all") {
        query = query.contains('content_categories', [input.category]);
      }

      // Apply search filter
      if (input.search && input.search.length > 0) {
        query = query.or(`content_title.ilike.%${input.search}%,content_description.ilike.%${input.search}%,content_account.ilike.%${input.search}%`);
      }

      // Apply sorting
      const sortColumn = input.sortBy === "date" ? "content_created_date" : 
                        input.sortBy === "impressions" ? "twitter_impressions" :
                        input.sortBy === "likes" ? "twitter_likes" : "twitter_retweets";
      
      query = query.order(sortColumn, { ascending: input.sortOrder === "asc" });

      // Apply limit
      query = query.limit(input.limit);

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch filtered content: ${error.message}`);
      }

      return data as ContentPiece[];
    }),

  // Auth procedures
  getCurrentUser: publicProcedure.query(async ({ ctx }) => {
    return ctx.user;
  }),

  signUp: publicProcedure
    .input(z.object({
      email: z.string().email(),
      password: z.string().min(6),
    }))
    .mutation(async ({ input }) => {
      const supabase = await createServerSupabaseClient();
      
      const { data, error } = await supabase.auth.signUp({
        email: input.email,
        password: input.password,
        options: {
          emailRedirectTo: `${process.env.CORS_ORIGIN || 'http://localhost:3001'}/auth/callback`,
        },
      });

      if (error) {
        throw new Error(`Sign up failed: ${error.message}`);
      }

      return { user: data.user, session: data.session };
    }),

  signIn: publicProcedure
    .input(z.object({
      email: z.string().email(),
      password: z.string(),
    }))
    .mutation(async ({ input }) => {
      const supabase = await createServerSupabaseClient();
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: input.email,
        password: input.password,
      });

      if (error) {
        throw new Error(`Sign in failed: ${error.message}`);
      }

      return { user: data.user, session: data.session };
    }),

  signOut: publicProcedure.mutation(async () => {
    const supabase = await createServerSupabaseClient();
    
    const { error } = await supabase.auth.signOut();

    if (error) {
      throw new Error(`Sign out failed: ${error.message}`);
    }

    return { success: true };
  }),
});

export type AppRouter = typeof appRouter;
