import type { NextRequest } from "next/server";
import { createServerSupabaseClient } from "./supabase";
import type { User } from "@supabase/supabase-js";

export async function createContext(req: NextRequest) {
  let user: User | null = null;
  
  try {
    const supabase = await createServerSupabaseClient();
    const { data: { user: authUser } } = await supabase.auth.getUser();
    user = authUser;
  } catch (error) {
    console.error("Error getting user session:", error);
    // Fail silently to not break non-auth operations
  }
  
  return {
    user,
    session: user ? { user } : null,
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;
