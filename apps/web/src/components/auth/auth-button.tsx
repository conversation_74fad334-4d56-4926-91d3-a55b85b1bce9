"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { trpc } from "@/utils/trpc";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LoginForm } from "./login-form";
import { toast } from "sonner";
import { User, LogOut, UserCircle } from "lucide-react";
import type { User as SupabaseUser } from "@supabase/supabase-js";

interface AuthButtonProps {
  user?: SupabaseUser | null;
}

export function AuthButton({ user }: AuthButtonProps) {
  const [showLoginForm, setShowLoginForm] = useState(false);
  const router = useRouter();
  
  const signOutMutation = trpc.signOut.useMutation({
    onSuccess: () => {
      toast.success("Successfully signed out!");
      router.refresh();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  
  const handleSignOut = () => {
    signOutMutation.mutate();
  };
  
  if (showLoginForm) {
    return (
      <div
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            setShowLoginForm(false);
          }
        }}
      >
        <div className="relative w-full max-w-md animate-in fade-in-0 zoom-in-95 duration-300">
          <button
            onClick={() => setShowLoginForm(false)}
            className="absolute -top-3 -right-3 bg-background border rounded-full w-8 h-8 flex items-center justify-center text-muted-foreground hover:text-foreground hover:bg-muted transition-colors z-10 shadow-lg"
          >
            ×
          </button>
          <LoginForm onClose={() => setShowLoginForm(false)} />
        </div>
      </div>
    );
  }
  
  if (user) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="flex items-center space-x-2">
            <UserCircle className="h-4 w-4" />
            <span className="hidden sm:inline">
              {user.email?.split('@')[0] || 'User'}
            </span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuItem disabled>
            <User className="mr-2 h-4 w-4" />
            <span className="text-sm text-muted-foreground">{user.email}</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            onClick={handleSignOut}
            disabled={signOutMutation.isPending}
          >
            <LogOut className="mr-2 h-4 w-4" />
            {signOutMutation.isPending ? "Signing out..." : "Sign out"}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }
  
  return (
    <Button 
      onClick={() => setShowLoginForm(true)}
      variant="default"
      className="flex items-center space-x-2"
    >
      <UserCircle className="h-4 w-4" />
      <span>Sign In</span>
    </Button>
  );
}