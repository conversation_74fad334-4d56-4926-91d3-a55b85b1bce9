"use client";

import { Card } from "./ui/card";
import { But<PERSON> } from "./ui/button";
import { ExternalLink, <PERSON>, Heart, Calendar, Hash, Repeat2 } from "lucide-react";

interface ContentItem {
  id: number;
  content_title: string | null;
  content_account: string;
  content_created_date: string;
  content_link: string;
  twitter_impressions: number;
  twitter_likes: number;
  twitter_retweets: number;
  twitter_content_type: string | null;
  content_categories: string[];
  content_types: string[];
  content_description?: string | null;
}

interface ContentTableProps {
  title: string;
  data: ContentItem[];
  titleColor: "primary" | "chart-1" | "chart-2" | "chart-3" | "chart-4" | "chart-5";
  isLoading?: boolean;
}

const colorClasses = {
  primary: "text-primary",
  "chart-1": "text-chart-1",
  "chart-2": "text-chart-2", 
  "chart-3": "text-chart-3",
  "chart-4": "text-chart-4",
  "chart-5": "text-chart-5"
};

function formatNumber(num: number) {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
}

function formatDate(dateStr: string) {
  return new Date(dateStr).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
}

export function ContentTable({ title, data, titleColor, isLoading }: ContentTableProps) {
  const colorClass = colorClasses[titleColor];

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="center-all justify-start gap-4">
          <div className={`w-1 h-10 rounded-full ${colorClass} bg-current`} />
          <h2 className="text-3xl font-bold text-center sm:text-left">
            {title}
          </h2>
        </div>
        
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="p-6 glass-effect border-0">
              <div className="space-y-4">
                <div className="h-5 bg-muted rounded animate-pulse" />
                <div className="h-4 bg-muted/60 rounded animate-pulse w-3/4" />
                <div className="flex gap-2">
                  <div className="h-6 bg-muted/40 rounded animate-pulse w-20" />
                  <div className="h-6 bg-muted/40 rounded animate-pulse w-20" />
                </div>
                <div className="flex justify-between items-center pt-2">
                  <div className="h-3 bg-muted/30 rounded animate-pulse w-16" />
                  <div className="h-8 bg-muted/40 rounded animate-pulse w-16" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="space-y-8">
        <div className="center-all justify-start gap-4">
          <div className={`w-1 h-10 rounded-full ${colorClass} bg-current`} />
          <h2 className="text-3xl font-bold text-center sm:text-left">
            {title}
          </h2>
        </div>
        
        <Card className="p-16 text-center glass-effect border-0">
          <div className="center-all flex-col space-y-6 text-muted-foreground">
            <Hash className="w-16 h-16 opacity-40" />
            <div className="space-y-2">
              <p className="text-xl font-semibold">No content found</p>
              <p className="text-base">Check back later for updates</p>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="center-all justify-start gap-4">
        <div className={`w-1 h-10 rounded-full ${colorClass} bg-current`} />
        <h2 className="text-3xl font-bold text-center sm:text-left">
          {title}
        </h2>
      </div>
      
      {/* Responsive card grid */}
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {data.map((item) => (
          <Card key={item.id} className="group p-6 card-hover border-0 glass-effect overflow-hidden">
            <div className="space-y-5">
              {/* Title and Categories */}
              <div className="space-y-3">
                <h3 className="font-bold text-lg leading-tight line-clamp-2 group-hover:text-primary transition-colors">
                  {item.content_title || "Untitled Content"}
                </h3>
                
                <div className="flex gap-2 flex-wrap">
                  {item.content_categories.slice(0, 2).map((category, idx) => (
                    <span 
                      key={idx} 
                      className="inline-flex items-center gap-1 bg-secondary/70 text-secondary-foreground px-3 py-1 rounded-full text-xs font-semibold"
                    >
                      <Hash className="w-3 h-3" />
                      {category}
                    </span>
                  ))}
                  {item.content_categories.length > 2 && (
                    <span className="inline-flex items-center bg-muted text-muted-foreground px-3 py-1 rounded-full text-xs font-medium">
                      +{item.content_categories.length - 2}
                    </span>
                  )}
                </div>
              </div>

              {/* Account and Type */}
              <div className="center-all justify-between">
                <span className="font-semibold text-sm">{item.content_account}</span>
                <span className="bg-accent/30 text-accent-foreground px-3 py-1 rounded-full text-xs font-semibold">
                  {item.twitter_content_type || "content"}
                </span>
              </div>

              {/* Stats - Only show non-zero values */}
              {(item.twitter_impressions > 0 || item.twitter_likes > 0 || item.twitter_retweets > 0) && (
                <div className="flex gap-4 py-4 border-t border-border/30 flex-wrap">
                  {item.twitter_impressions > 0 && (
                    <div className="center-all justify-start gap-2">
                      <Eye className="w-4 h-4 text-muted-foreground" />
                      <span className="font-mono text-sm font-bold">
                        {formatNumber(item.twitter_impressions)}
                      </span>
                    </div>
                  )}
                  {item.twitter_likes > 0 && (
                    <div className="center-all justify-start gap-2">
                      <Heart className="w-4 h-4 text-muted-foreground" />
                      <span className="font-mono text-sm font-bold">
                        {formatNumber(item.twitter_likes)}
                      </span>
                    </div>
                  )}
                  {item.twitter_retweets > 0 && (
                    <div className="center-all justify-start gap-2">
                      <Repeat2 className="w-4 h-4 text-muted-foreground" />
                      <span className="font-mono text-sm font-bold">
                        {formatNumber(item.twitter_retweets)}
                      </span>
                    </div>
                  )}
                </div>
              )}

              {/* Footer */}
              <div className="center-all justify-between pt-2">
                <div className="center-all gap-2 text-xs text-muted-foreground">
                  <Calendar className="w-4 h-4" />
                  <span className="font-medium">{formatDate(item.content_created_date)}</span>
                </div>
                
                <Button
                  size="sm"
                  className="h-9 px-4 text-xs font-semibold gap-2"
                  asChild
                >
                  <a
                    href={item.content_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="center-all gap-2"
                  >
                    <span>View</span>
                    <ExternalLink className="w-3 h-3" />
                  </a>
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}