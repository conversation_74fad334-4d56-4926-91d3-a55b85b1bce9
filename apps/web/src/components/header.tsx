"use client";
import Link from "next/link";
import { trpc } from "@/utils/trpc";
import { AuthButton } from "@/components/auth/auth-button";
import { BarChart3, Zap } from "lucide-react";

export default function Header() {
  const { data: user } = trpc.getCurrentUser.useQuery();
  
  return (
    <header className="sticky top-0 z-50 w-full glass-effect border-b border-border/30">
      <div className="content-container">
        <div className="center-all justify-between h-20">
          {/* Brand */}
          <Link href="/" className="hover:opacity-90 transition-opacity duration-300">
            <span className="font-bold text-2xl tracking-tight text-attention-blue">ATTENTION</span>
          </Link>
          
          {/* Auth Button */}
          <AuthButton user={user} />
        </div>
      </div>
    </header>
  );
}
