{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port=3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/ssr": "^0.6.1", "@tanstack/react-form": "^1.12.3", "@tanstack/react-query": "^5.80.5", "@trpc/client": "^11.4.2", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.487.0", "next": "15.3.0", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "zod": "^4.0.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4.1.10", "typescript": "^5", "@tanstack/react-query-devtools": "^5.80.5"}}